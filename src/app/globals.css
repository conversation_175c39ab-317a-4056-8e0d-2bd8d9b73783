@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-palette-green: var(--palette-green);
  --color-palette-black: var(--palette-black);
  --color-palette-black-200: var(--palette-black-200);
  --color-palette-white: var(--palette-white);
  --color-palette-gray: var(--palette-gray);
  --color-palette-gray-200: var(--palette-gray-200);

  --color-typography: var(--palette-black-200);
  --color-typography-200: var(--palette-gray);
  --color-typography-300: var(--palette-white);
  --color-typography-400: var(--palette-gray-200);

  --text-xxs: 0.625rem;

  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-sf-compact-text);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  /* Project Color Palettes */
  --palette-green: oklch(0.66 0.1602 152.37);

  --palette-black: oklch(0 0 0);
  --palette-black-200: oklch(0.145 0 0);

  --palette-white: oklch(1 0 0);

  --palette-gray: oklch(0.54 0 0);
  --palette-gray-200: oklch(0.33 0 0);

  --radius: 0.5rem;
  --background: var(--palette-green);
  --foreground: var(--color-typography);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    min-height: 100vh;

    @apply bg-background text-foreground overflow-x-hidden overflow-y-auto;
    background: radial-gradient(
      321.35% 242.03% at -7.12% -17.43%,
      #000 0%,
      #27ae60 100%
    );
  }

  html {
    /* @apply bg-background; */
  }
}

/* body {

} */

@layer components {
  .wrapper {
    @apply px-3;
  }
}

.grid-bg {
  background-image: linear-gradient(
      rgba(255, 255, 255, 0.1) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.hexagon {
  width: 200px;
  height: 173px;
  position: relative;
  clip-path: polygon(25% 0%, 75% 0%, 100% 50%, 75% 100%, 25% 100%, 0% 50%);
}
