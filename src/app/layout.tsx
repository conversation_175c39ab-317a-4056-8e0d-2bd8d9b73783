import type { <PERSON><PERSON><PERSON> } from "next";
import localFont from "next/font/local";
import "./globals.css";
import { siteConfig } from "@/config/site";
import EvmWalletProvider from "@/providers/EvmWalletProvider";
import UserProvider from "@/providers/UserProvider";
import { Toaster } from "react-hot-toast";

// Add SF Compact Text font
const sfCompactText = localFont({
  variable: "--font-sf-compact-text",
  src: [
    {
      path: "../assets/fonts/sc/SFCompactText-Light.otf",
      weight: "300",
      style: "normal",
    },
    {
      path: "../assets/fonts/sc/SFCompactText-LightItalic.otf",
      weight: "300",
      style: "italic",
    },
    {
      path: "../assets/fonts/sc/SFCompactText-Regular.otf",
      weight: "400",
      style: "normal",
    },
    {
      path: "../assets/fonts/sc/SFCompactText-RegularItalic.otf",
      weight: "400",
      style: "italic",
    },
    {
      path: "../assets/fonts/sc/SFCompactText-Medium.otf",
      weight: "500",
      style: "normal",
    },
    {
      path: "../assets/fonts/sc/SFCompactText-MediumItalic.otf",
      weight: "500",
      style: "italic",
    },
    {
      path: "../assets/fonts/sc/SFCompactText-Semibold.otf",
      weight: "600",
      style: "normal",
    },
    {
      path: "../assets/fonts/sc/SFCompactText-Bold.otf",
      weight: "700",
      style: "normal",
    },
    {
      path: "../assets/fonts/sc/SFCompactText-BoldItalic.otf",
      weight: "700",
      style: "italic",
    },
    {
      path: "../assets/fonts/sc/SFCompactText-Heavy.otf",
      weight: "800",
      style: "normal",
    },
    {
      path: "../assets/fonts/sc/SFCompactText-HeavyItalic.otf",
      weight: "800",
      style: "italic",
    },
  ],
});

export const metadata: Metadata = {
  title: siteConfig.name,
  description: siteConfig.description,
  openGraph: {
    title: siteConfig.name,
    description: siteConfig.description,
    url: siteConfig.url,
    images: [siteConfig.ogImage],
  },
  twitter: {
    card: "summary_large_image",
    site: siteConfig.links.twitter,
    title: siteConfig.name,
    images: [siteConfig.ogImage],
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        suppressHydrationWarning
        className={`${sfCompactText.variable} font-sans antialiased`}
      >
        <EvmWalletProvider>
          <UserProvider>
            <Toaster
              position="top-center"
              reverseOrder={false}
              toastOptions={{
                style: {
                  border: "1px solid #E4E7EC",
                  borderRadius: 15,
                  padding: "16px",
                  color: "#000",
                  fontSize: 15,
                  fontWeight: 400,
                },
                duration: 1000,
              }}
            />
            {children}
          </UserProvider>
        </EvmWalletProvider>
      </body>
    </html>
  );
}
