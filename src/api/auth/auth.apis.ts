import { request } from "@/utils/axios-utils";
import { AxiosResponse } from "axios";
import { IConnectWallet, RConnectWallet, RLogout } from "./auth.types";

export const connectWalletRequest = async (
  formdata: IConnectWallet
): Promise<AxiosResponse<RConnectWallet>> => {
  return request({
    url: "/authentication/connect-wallet",
    method: "post",
    data: formdata,
  });
};

export const logoutRequest = async (): Promise<AxiosResponse<RLogout>> => {
  return request({
    url: "/authentication/logout",
    method: "post",
  });
};
