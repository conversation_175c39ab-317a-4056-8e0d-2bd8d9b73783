import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";
import { ErrorResponse } from "../type";
import { IConnectWallet, RConnectWallet, RLogout } from "./auth.types";
import { connectWalletRequest, logoutRequest } from "./auth.apis";

export const useConnectWallet = (
  onError: (error: AxiosError<ErrorResponse>) => void,
  onSuccess: (data: AxiosResponse<RConnectWallet>) => void
) => {
  const queryClient = useQueryClient();
  return useMutation<
    AxiosResponse<RConnectWallet>,
    AxiosError<ErrorResponse>,
    IConnectWallet
  >({
    mutationFn: connectWalletRequest,
    onError,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["user"] });
      onSuccess(data);
    },
  });
};

export const useLogout = (
  onError: (error: AxiosError<ErrorResponse>) => void,
  onSuccess: (data: AxiosResponse<RLogout>) => void
) => {
  return useMutation<
    AxiosResponse<RLogout>,
    AxiosError<ErrorResponse>,
    undefined
  >({
    mutationFn: logoutRequest,
    onError,
    onSuccess,
  });
};
