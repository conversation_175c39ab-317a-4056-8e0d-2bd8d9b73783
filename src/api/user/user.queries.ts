import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  getAuthenticatedUserRequest,
  updateAvatarRequest,
  updateUserRequest,
} from "./user.apis";
import Cookies from "js-cookie";
import { AxiosError } from "axios";
import { AxiosResponse } from "axios";
import {
  IUpdateAvatar,
  IUpdateUser,
  RUpdateAvatar,
  RUpdateUser,
} from "./user.types";
import { ErrorResponse } from "../type";

export const useGetAuthenticatedUser = () => {
  const token = Cookies.get("Authentication");
  const enabled = !!token;

  const { data, isError, isSuccess, error } = useQuery({
    queryKey: ["user"],
    queryFn: getAuthenticatedUserRequest,
    refetchOnWindowFocus: true,
    refetchInterval: 5 * 60 * 1000,
    refetchIntervalInBackground: true,
    staleTime: 30 * 1000,
    retry: 1,
    enabled,
  });
  const user = data?.data?.data;

  return { user, isError, isSuccess, error };
};

export const useUpdateAvatar = (
  onError: (error: AxiosError<ErrorResponse>) => void,
  onSuccess: (data: AxiosResponse<RUpdateAvatar>) => void
) => {
  const queryClient = useQueryClient();
  return useMutation<
    AxiosResponse<RUpdateAvatar>,
    AxiosError<ErrorResponse>,
    IUpdateAvatar
  >({
    mutationFn: updateAvatarRequest,
    onError,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["user"] });
      onSuccess(data);
    },
  });
};

export const useUpdateUser = (
  onError: (error: AxiosError<ErrorResponse>) => void,
  onSuccess: (data: AxiosResponse<RUpdateUser>) => void
) => {
  const queryClient = useQueryClient();
  return useMutation<
    AxiosResponse<RUpdateUser>,
    AxiosError<ErrorResponse>,
    IUpdateUser
  >({
    mutationFn: updateUserRequest,
    onError,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["user"] });
      onSuccess(data);
    },
  });
};
