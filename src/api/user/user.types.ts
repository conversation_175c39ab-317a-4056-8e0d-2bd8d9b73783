import { User } from "@/constants/types";
import { SuccessResponse } from "../type";

// Get Wallet Nonce
export interface IGetWalletNonce {
  walletAddress: string;
}

export interface RGetWalletNonce extends SuccessResponse {
  data: number;
}

// Get Authenticated User
export interface RGetAuthenticatedUser extends SuccessResponse {
  data: User;
}

// Update Avatar
export type IUpdateAvatar = FormData;

export type RUpdateAvatar = SuccessResponse;

// Update user
export interface IUpdateUser {
  socials?: Record<string, string>;
  avatar?: string;
}

export type RUpdateUser = SuccessResponse;
