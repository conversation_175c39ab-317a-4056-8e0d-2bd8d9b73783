import { AxiosResponse } from "axios";
import {
  IGetWalletNonce,
  IUpdateAvatar,
  IUpdateUser,
  RGetAuthenticatedUser,
  RGetWalletNonce,
  RUpdateAvatar,
  RUpdateUser,
} from "./user.types";
import { request } from "@/utils/axios-utils";

export const getWalletNonceRequest = async (
  formdata: IGetWalletNonce
): Promise<AxiosResponse<RGetWalletNonce>> => {
  const { walletAddress } = formdata;
  return request({
    url: `/user/nonce/${walletAddress}`,
    method: "get",
  });
};

export const getAuthenticatedUserRequest = async (): Promise<
  AxiosResponse<RGetAuthenticatedUser>
> => {
  return request({
    url: `/user`,
    method: "get",
  });
};

export const updateAvatarRequest = async (
  formData: IUpdateAvatar
): Promise<AxiosResponse<RUpdateAvatar>> => {
  return request({
    url: `/user/update-avatar`,
    method: "put",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
};

export const updateUserRequest = async (
  formdata: IUpdateUser
): Promise<AxiosResponse<RUpdateUser>> => {
  return request({
    url: `/user`,
    method: "put",
    data: formdata,
  });
};
