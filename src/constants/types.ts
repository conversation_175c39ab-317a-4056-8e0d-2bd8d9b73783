export interface User {
  id: string;
  walletAddress: string;
  avatar: string;
  socials: Record<string, string>;
  nonce: number;
  chainId: number;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface ChainsConfig {
  [chainId: number]: {
    name: string;
    config: {
      id: number;
      name: string;
      nativeCurrency: {
        decimals: number;
        name: string;
        symbol: string;
        icon?: string;
      };
      rpcUrls: {
        default: {
          http: string[];
          webSocket?: string[];
        };
        public?: {
          http: string[];
          webSocket?: string[];
        };
      };
      blockExplorers?: {
        default: {
          name: string;
          url: string;
        };
      };
      testnet?: boolean;
    };
  };
}

export type Provider = {
  request: ({
    method,
    params,
  }: {
    method: string;
    params?: unknown[];
  }) => Promise<unknown>;
};
