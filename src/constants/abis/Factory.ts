export const FactoryABI = [
  {
    type: "constructor",
    inputs: [
      {
        name: "_feeRecipient",
        type: "address",
        internalType: "address",
      },
      {
        name: "_teamFinanceLock",
        type: "address",
        internalType: "address",
      },
      {
        name: "_uniswapRouter",
        type: "address",
        internalType: "address",
      },
      {
        name: "_presaleImplementation",
        type: "address",
        internalType: "address",
      },
      {
        name: "_fairlaunchImplementation",
        type: "address",
        internalType: "address",
      },
      {
        name: "_buybackServiceImplementation",
        type: "address",
        internalType: "address",
      },
      {
        name: "_nativeTokenPriceFeed",
        type: "address",
        internalType: "address",
      },
    ],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "PRECISION",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "addPricingTier",
    inputs: [
      { name: "_name", type: "string", internalType: "string" },
      {
        name: "_maxContributionUSD",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "_fixedFeeUSD", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "allSales",
    inputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "buybackServiceImplementation",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "cancelSale",
    inputs: [
      {
        name: "_saleContract",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "convertNativeToUSD",
    inputs: [
      {
        name: "_nativeAmount",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "createBuybackService",
    inputs: [],
    outputs: [
      {
        name: "buybackServiceAddress",
        type: "address",
        internalType: "address",
      },
    ],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "createFairlaunch",
    inputs: [
      { name: "_creator", type: "address", internalType: "address" },
      { name: "_saleToken", type: "address", internalType: "address" },
      {
        name: "_totalTokensAllocated",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "_softCap", type: "uint256", internalType: "uint256" },
      { name: "_hardCap", type: "uint256", internalType: "uint256" },
      {
        name: "_liquidityPercent",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_lockupDuration",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "_startTime", type: "uint256", internalType: "uint256" },
      { name: "_endTime", type: "uint256", internalType: "uint256" },
      { name: "_buybackEnabled", type: "bool", internalType: "bool" },
      { name: "_referralEnabled", type: "bool", internalType: "bool" },
      { name: "_bonusEnabled", type: "bool", internalType: "bool" },
      {
        name: "_manualContributionEnabled",
        type: "bool",
        internalType: "bool",
      },
      {
        name: "_listingRatePercent",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_startMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_endMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_referralFeeRate",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_buybackPercentage",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_selectedTierIndex",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_acceptedTokens",
        type: "address[]",
        internalType: "address[]",
      },
      { name: "_dexRouter", type: "address", internalType: "address" },
    ],
    outputs: [
      {
        name: "fairlaunchAddress",
        type: "address",
        internalType: "address",
      },
    ],
    stateMutability: "payable",
  },
  {
    type: "function",
    name: "createPresale",
    inputs: [
      { name: "_creator", type: "address", internalType: "address" },
      { name: "_saleToken", type: "address", internalType: "address" },
      { name: "_softCap", type: "uint256", internalType: "uint256" },
      { name: "_hardCap", type: "uint256", internalType: "uint256" },
      {
        name: "_presaleRate",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_listingRate",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_liquidityPercent",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_lockupDuration",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "_startTime", type: "uint256", internalType: "uint256" },
      { name: "_endTime", type: "uint256", internalType: "uint256" },
      { name: "_buybackEnabled", type: "bool", internalType: "bool" },
      { name: "_referralEnabled", type: "bool", internalType: "bool" },
      { name: "_bonusEnabled", type: "bool", internalType: "bool" },
      {
        name: "_manualContributionEnabled",
        type: "bool",
        internalType: "bool",
      },
      {
        name: "_startMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_endMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_referralFeeRate",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_buybackPercentage",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_selectedTierIndex",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_acceptedTokens",
        type: "address[]",
        internalType: "address[]",
      },
      { name: "_dexRouter", type: "address", internalType: "address" },
    ],
    outputs: [
      {
        name: "presaleAddress",
        type: "address",
        internalType: "address",
      },
    ],
    stateMutability: "payable",
  },
  {
    type: "function",
    name: "creatorSales",
    inputs: [
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "defaultRouter",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "emergencyWithdraw",
    inputs: [
      { name: "_token", type: "address", internalType: "address" },
      { name: "_recipient", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "factoryAdmin",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "fairlaunchImplementation",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "feeRecipient",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getActiveSales",
    inputs: [],
    outputs: [{ name: "", type: "address[]", internalType: "address[]" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getAllPricingTiers",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "tuple[]",
        internalType: "struct FactoryLib.PricingTier[]",
        components: [
          { name: "name", type: "string", internalType: "string" },
          {
            name: "maxContributionUSD",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "fixedFeeUSD",
            type: "uint256",
            internalType: "uint256",
          },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getAllPricingTiersWithNative",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "tuple[]",
        internalType: "struct IFactory.PricingTierWithNative[]",
        components: [
          { name: "name", type: "string", internalType: "string" },
          {
            name: "maxContributionUSD",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "fixedFeeUSD",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "priceInNative",
            type: "uint256",
            internalType: "uint256",
          },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getAllSales",
    inputs: [],
    outputs: [{ name: "", type: "address[]", internalType: "address[]" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getAllSalesCount",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getCreatorAnalytics",
    inputs: [{ name: "_creator", type: "address", internalType: "address" }],
    outputs: [
      { name: "totalSales", type: "uint256", internalType: "uint256" },
      { name: "activeSales", type: "uint256", internalType: "uint256" },
      {
        name: "finishedSales",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "cancelledSales",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getCreatorSales",
    inputs: [{ name: "_creator", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "address[]", internalType: "address[]" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getFairlaunchTokenRequirements",
    inputs: [
      { name: "_saleToken", type: "address", internalType: "address" },
      {
        name: "_totalTokensAllocated",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_listingRatePercent",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_liquidityPercent",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [
      {
        name: "distribution",
        type: "tuple",
        internalType: "struct IFactory.TokenDistribution",
        components: [
          {
            name: "tokensForSale",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "tokensForLiquidity",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "totalTokensNeeded",
            type: "uint256",
            internalType: "uint256",
          },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getNativeTokenPriceFeed",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getPlatformAnalytics",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "tuple",
        internalType: "struct FactoryLib.PlatformAnalytics",
        components: [
          {
            name: "totalSales",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "totalPresales",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "totalFairLaunches",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "activeSales",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "finishedSales",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "cancelledSales",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "feeRecipientAddress",
            type: "address",
            internalType: "address",
          },
          {
            name: "teamFinanceLockAddress",
            type: "address",
            internalType: "address",
          },
          {
            name: "routerAddress",
            type: "address",
            internalType: "address",
          },
          {
            name: "pricingTiersCount",
            type: "uint256",
            internalType: "uint256",
          },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getPlatformConfig",
    inputs: [],
    outputs: [
      { name: "recipient", type: "address", internalType: "address" },
      { name: "lock", type: "address", internalType: "address" },
      {
        name: "_defaultRouter",
        type: "address",
        internalType: "address",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getPresaleTokenRequirements",
    inputs: [
      { name: "_saleToken", type: "address", internalType: "address" },
      { name: "_hardCap", type: "uint256", internalType: "uint256" },
      {
        name: "_presaleRate",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_listingRate",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_liquidityPercent",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [
      {
        name: "",
        type: "tuple",
        internalType: "struct IFactory.TokenDistribution",
        components: [
          {
            name: "tokensForSale",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "tokensForLiquidity",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "totalTokensNeeded",
            type: "uint256",
            internalType: "uint256",
          },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getPresaleTokenRequirements",
    inputs: [
      { name: "_saleToken", type: "address", internalType: "address" },
      { name: "_hardCap", type: "uint256", internalType: "uint256" },
      {
        name: "_presaleRate",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_listingRate",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_liquidityPercent",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_startMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [
      {
        name: "distribution",
        type: "tuple",
        internalType: "struct IFactory.TokenDistribution",
        components: [
          {
            name: "tokensForSale",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "tokensForLiquidity",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "totalTokensNeeded",
            type: "uint256",
            internalType: "uint256",
          },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getPricingTier",
    inputs: [{ name: "_tierIndex", type: "uint256", internalType: "uint256" }],
    outputs: [
      { name: "name", type: "string", internalType: "string" },
      {
        name: "maxContributionUSD",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "fixedFeeUSD", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getPricingTiersCount",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getSaleAtIndex",
    inputs: [{ name: "_index", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getSaleInfo",
    inputs: [
      { name: "_saleAddress", type: "address", internalType: "address" },
    ],
    outputs: [
      { name: "creator", type: "address", internalType: "address" },
      {
        name: "tokenAddress",
        type: "address",
        internalType: "address",
      },
      {
        name: "creationTime",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "isPresale", type: "bool", internalType: "bool" },
      { name: "isActive", type: "bool", internalType: "bool" },
      { name: "isFinalized", type: "bool", internalType: "bool" },
      { name: "isCancelled", type: "bool", internalType: "bool" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getTierFeeInNative",
    inputs: [{ name: "_tierIndex", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getTierForContribution",
    inputs: [
      {
        name: "_contributionUSD",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [
      { name: "tierIndex", type: "uint256", internalType: "uint256" },
      { name: "name", type: "string", internalType: "string" },
      {
        name: "maxContributionUSD",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "fixedFeeUSD", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "nativeTokenPriceFeed",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "operators",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "owner",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "pause",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "pauseSale",
    inputs: [
      {
        name: "_saleContract",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "paused",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "presaleImplementation",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "pricingTiers",
    inputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    outputs: [
      { name: "name", type: "string", internalType: "string" },
      {
        name: "maxContributionUSD",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "fixedFeeUSD", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "registerSale",
    inputs: [
      {
        name: "_saleAddress",
        type: "address",
        internalType: "address",
      },
      {
        name: "_tokenAddress",
        type: "address",
        internalType: "address",
      },
      { name: "_creator", type: "address", internalType: "address" },
      { name: "_isPresale", type: "bool", internalType: "bool" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "removePricingTier",
    inputs: [{ name: "_tierIndex", type: "uint256", internalType: "uint256" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "renounceOwnership",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "saleInfo",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [
      { name: "creator", type: "address", internalType: "address" },
      {
        name: "tokenAddress",
        type: "address",
        internalType: "address",
      },
      {
        name: "creationTime",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "isPresale", type: "bool", internalType: "bool" },
      { name: "isActive", type: "bool", internalType: "bool" },
      { name: "isFinalized", type: "bool", internalType: "bool" },
      { name: "isCancelled", type: "bool", internalType: "bool" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "setBuybackServiceImplementation",
    inputs: [
      {
        name: "_buybackServiceImplementation",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setDefaultNativeTokenPriceFeed",
    inputs: [{ name: "_priceFeed", type: "address", internalType: "address" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setOperator",
    inputs: [
      { name: "_operator", type: "address", internalType: "address" },
      { name: "_status", type: "bool", internalType: "bool" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setPaused",
    inputs: [{ name: "_paused", type: "bool", internalType: "bool" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "teamFinanceLock",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "transferOwnership",
    inputs: [{ name: "newOwner", type: "address", internalType: "address" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "uniswapRouter",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "unpause",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "updateDefaultRouter",
    inputs: [
      {
        name: "_newRouterAddress",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "updateFeeRecipient",
    inputs: [
      {
        name: "_newRecipient",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "updatePricingTier",
    inputs: [
      { name: "_tierIndex", type: "uint256", internalType: "uint256" },
      { name: "_name", type: "string", internalType: "string" },
      {
        name: "_maxContributionUSD",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "_fixedFeeUSD", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "updateSaleStatus",
    inputs: [
      {
        name: "_saleAddress",
        type: "address",
        internalType: "address",
      },
      { name: "_isActive", type: "bool", internalType: "bool" },
      { name: "_isFinalized", type: "bool", internalType: "bool" },
      { name: "_isCancelled", type: "bool", internalType: "bool" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "updateTeamFinanceLock",
    inputs: [
      {
        name: "_newLockAddress",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "event",
    name: "BuybackServiceCreated",
    inputs: [
      {
        name: "buybackService",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "creator",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "BuybackServiceImplementationUpdated",
    inputs: [
      {
        name: "newImplementation",
        type: "address",
        indexed: false,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "DefaultPriceFeedUpdated",
    inputs: [
      {
        name: "priceFeed",
        type: "address",
        indexed: false,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "EmergencyWithdraw",
    inputs: [
      {
        name: "token",
        type: "address",
        indexed: false,
        internalType: "address",
      },
      {
        name: "recipient",
        type: "address",
        indexed: false,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "FactoryPaused",
    inputs: [
      {
        name: "isPaused",
        type: "bool",
        indexed: false,
        internalType: "bool",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "FairLaunchCreated",
    inputs: [
      {
        name: "fairlaunchContract",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "creator",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "token",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "FeeRecipientUpdated",
    inputs: [
      {
        name: "newRecipient",
        type: "address",
        indexed: false,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "OperatorStatusChanged",
    inputs: [
      {
        name: "operator",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "status",
        type: "bool",
        indexed: false,
        internalType: "bool",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "OwnershipTransferred",
    inputs: [
      {
        name: "previousOwner",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "newOwner",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "PresaleCreated",
    inputs: [
      {
        name: "presaleContract",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "creator",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "token",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "PricingTierAdded",
    inputs: [
      {
        name: "name",
        type: "string",
        indexed: false,
        internalType: "string",
      },
      {
        name: "maxContributionUSD",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "fixedFeeUSD",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "PricingTierRemoved",
    inputs: [
      {
        name: "tierIndex",
        type: "uint256",
        indexed: true,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "PricingTierUpdated",
    inputs: [
      {
        name: "tierIndex",
        type: "uint256",
        indexed: true,
        internalType: "uint256",
      },
      {
        name: "name",
        type: "string",
        indexed: false,
        internalType: "string",
      },
      {
        name: "maxContributionUSD",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "fixedFeeUSD",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "RouterUpdated",
    inputs: [
      {
        name: "newRouterAddress",
        type: "address",
        indexed: false,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "SaleCancelled",
    inputs: [
      {
        name: "saleContract",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "SalePaused",
    inputs: [
      {
        name: "saleContract",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "isPaused",
        type: "bool",
        indexed: false,
        internalType: "bool",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "TeamFinanceLockUpdated",
    inputs: [
      {
        name: "newLockAddress",
        type: "address",
        indexed: false,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  { type: "error", name: "FailedDeployment", inputs: [] },
  {
    type: "error",
    name: "InsufficientBalance",
    inputs: [
      { name: "balance", type: "uint256", internalType: "uint256" },
      { name: "needed", type: "uint256", internalType: "uint256" },
    ],
  },
  {
    type: "error",
    name: "OwnableInvalidOwner",
    inputs: [{ name: "owner", type: "address", internalType: "address" }],
  },
  {
    type: "error",
    name: "OwnableUnauthorizedAccount",
    inputs: [{ name: "account", type: "address", internalType: "address" }],
  },
  { type: "error", name: "ReentrancyGuardReentrantCall", inputs: [] },
  {
    type: "error",
    name: "SafeERC20FailedOperation",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
  },
];
