export const FairlaunchABI = [
  { type: "constructor", inputs: [], stateMutability: "nonpayable" },
  { type: "receive", stateMutability: "payable" },
  {
    type: "function",
    name: "acceptedTokens",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "acceptedTokensList",
    inputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "addManualContribution",
    inputs: [
      { name: "contributor", type: "address", internalType: "address" },
      { name: "amount", type: "uint256", internalType: "uint256" },
      { name: "referrer", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "buybackAmount",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "buybackPercentage",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "buybackService",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "address",
        internalType: "contract IBuybackService",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "calculateBuybackAmount",
    inputs: [
      {
        name: "_totalRaised",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_buybackPercentage",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "calculateLiquidityAmount",
    inputs: [
      {
        name: "_totalRaised",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "liquidityPercent",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "calculateLiquidityTokenAmount",
    inputs: [
      {
        name: "_liquidityAmount",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "_listingRate", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "calculateListingRate",
    inputs: [
      { name: "_finalRate", type: "uint256", internalType: "uint256" },
      {
        name: "listingRatePercent",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "calculateMinimumOutput",
    inputs: [
      { name: "amount", type: "uint256", internalType: "uint256" },
      {
        name: "slippageTolerance",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "cancelSale",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "claim",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "claimAffiliateRewards",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "configureBonusMultipliers",
    inputs: [
      {
        name: "_startMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_endMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "contributeETH",
    inputs: [{ name: "referrer", type: "address", internalType: "address" }],
    outputs: [],
    stateMutability: "payable",
  },
  {
    type: "function",
    name: "contributeTokens",
    inputs: [
      { name: "token", type: "address", internalType: "address" },
      { name: "amount", type: "uint256", internalType: "uint256" },
      { name: "referrer", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "contributeTokens",
    inputs: [
      { name: "token", type: "address", internalType: "address" },
      { name: "amount", type: "uint256", internalType: "uint256" },
      { name: "referrer", type: "address", internalType: "address" },
      { name: "minEthOutput", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "defaultReferrer",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "depositTokens",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "endMultiplier",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "estimateETHOutput",
    inputs: [
      { name: "token", type: "address", internalType: "address" },
      { name: "amount", type: "uint256", internalType: "uint256" },
      { name: "router", type: "address", internalType: "address" },
    ],
    outputs: [
      {
        name: "expectedOutput",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "estimateETHOutput",
    inputs: [
      { name: "token", type: "address", internalType: "address" },
      { name: "amount", type: "uint256", internalType: "uint256" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "executeBuyback",
    inputs: [
      { name: "minTokensOut", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "fairLaunchInfo",
    inputs: [],
    outputs: [
      { name: "saleToken", type: "address", internalType: "address" },
      {
        name: "totalTokensAllocated",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "softCap", type: "uint256", internalType: "uint256" },
      { name: "hardCap", type: "uint256", internalType: "uint256" },
      {
        name: "liquidityPercent",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "lockupDuration",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "startTime", type: "uint256", internalType: "uint256" },
      { name: "endTime", type: "uint256", internalType: "uint256" },
      {
        name: "listingRatePercent",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "referralEnabled", type: "bool", internalType: "bool" },
      { name: "buybackEnabled", type: "bool", internalType: "bool" },
      { name: "bonusEnabled", type: "bool", internalType: "bool" },
      {
        name: "manualContributionEnabled",
        type: "bool",
        internalType: "bool",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "finalRate",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "finalize",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "getBuybackInfo",
    inputs: [],
    outputs: [
      { name: "amount", type: "uint256", internalType: "uint256" },
      {
        name: "timeUntilNextBuyback",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getCurrentRate",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getETHValueParams",
    inputs: [
      { name: "token", type: "address", internalType: "address" },
      { name: "", type: "uint256", internalType: "uint256" },
      { name: "router", type: "address", internalType: "address" },
    ],
    outputs: [
      { name: "path", type: "address[]", internalType: "address[]" },
      {
        name: "recipientAddress",
        type: "address",
        internalType: "address",
      },
      { name: "deadline", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getFullState",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "tuple",
        internalType: "struct FairLaunchLib.FairLaunchFullState",
        components: [
          {
            name: "info",
            type: "tuple",
            internalType: "struct FairLaunchLib.FairLaunchInfo",
            components: [
              {
                name: "saleToken",
                type: "address",
                internalType: "address",
              },
              {
                name: "totalTokensAllocated",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "softCap",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "hardCap",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "liquidityPercent",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "lockupDuration",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "startTime",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "endTime",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "listingRatePercent",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "referralEnabled",
                type: "bool",
                internalType: "bool",
              },
              {
                name: "buybackEnabled",
                type: "bool",
                internalType: "bool",
              },
              {
                name: "bonusEnabled",
                type: "bool",
                internalType: "bool",
              },
              {
                name: "manualContributionEnabled",
                type: "bool",
                internalType: "bool",
              },
            ],
          },
          {
            name: "config",
            type: "tuple",
            internalType: "struct FairLaunchLib.FairLaunchConfig",
            components: [
              {
                name: "referralFeeRate",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "startMultiplier",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "endMultiplier",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "buybackPercentage",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "liquidityLockingPaused",
                type: "bool",
                internalType: "bool",
              },
              {
                name: "initialized",
                type: "bool",
                internalType: "bool",
              },
              {
                name: "featuresInitialized",
                type: "bool",
                internalType: "bool",
              },
            ],
          },
          {
            name: "progress",
            type: "tuple",
            internalType: "struct FairLaunchLib.FairLaunchProgress",
            components: [
              {
                name: "totalRaised",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "totalTokensSold",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "currentRate",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "finalRate",
                type: "uint256",
                internalType: "uint256",
              },
              { name: "isActive", type: "bool", internalType: "bool" },
              {
                name: "isFinalized",
                type: "bool",
                internalType: "bool",
              },
              {
                name: "isCancelled",
                type: "bool",
                internalType: "bool",
              },
              {
                name: "buybackAmount",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "nextBuybackTime",
                type: "uint256",
                internalType: "uint256",
              },
            ],
          },
          {
            name: "addresses",
            type: "tuple",
            internalType: "struct FairLaunchLib.FairLaunchAddresses",
            components: [
              {
                name: "teamFinanceLock",
                type: "address",
                internalType: "address",
              },
              {
                name: "uniswapRouter",
                type: "address",
                internalType: "address",
              },
              {
                name: "buybackService",
                type: "address",
                internalType: "address",
              },
            ],
          },
          {
            name: "tokens",
            type: "tuple",
            internalType: "struct FairLaunchLib.FairLaunchTokenInfo",
            components: [
              {
                name: "saleToken",
                type: "address",
                internalType: "address",
              },
              {
                name: "acceptedTokens",
                type: "address[]",
                internalType: "address[]",
              },
            ],
          },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getHasRequiredTokens",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getIsFinalized",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getLatestETHPrice",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getSaleInfo",
    inputs: [],
    outputs: [
      { name: "raised", type: "uint256", internalType: "uint256" },
      { name: "tokensSold", type: "uint256", internalType: "uint256" },
      { name: "active", type: "bool", internalType: "bool" },
      { name: "finalized", type: "bool", internalType: "bool" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getUserInfo",
    inputs: [{ name: "user", type: "address", internalType: "address" }],
    outputs: [
      {
        name: "contribution",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "tokensClaimed",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "hasRefunded", type: "bool", internalType: "bool" },
      {
        name: "referralEarnings",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getUserInfoState",
    inputs: [{ name: "user", type: "address", internalType: "address" }],
    outputs: [
      {
        name: "",
        type: "tuple",
        internalType: "struct FairLaunchLib.UserInfoState",
        components: [
          {
            name: "contribution",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "tokensAllocated",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "tokensClaimed",
            type: "uint256",
            internalType: "uint256",
          },
          { name: "hasRefunded", type: "bool", internalType: "bool" },
          {
            name: "referralEarnings",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "referralCount",
            type: "uint256",
            internalType: "uint256",
          },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "hasEnoughTokensForSale",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "initialize",
    inputs: [
      { name: "_saleToken", type: "address", internalType: "address" },
      {
        name: "_totalTokensAllocated",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "_softCap", type: "uint256", internalType: "uint256" },
      { name: "_hardCap", type: "uint256", internalType: "uint256" },
      {
        name: "_liquidityPercent",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_lockupDuration",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "_startTime", type: "uint256", internalType: "uint256" },
      { name: "_endTime", type: "uint256", internalType: "uint256" },
      { name: "_creator", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "initializeFeatures",
    inputs: [
      { name: "_referralEnabled", type: "bool", internalType: "bool" },
      { name: "_buybackEnabled", type: "bool", internalType: "bool" },
      { name: "_bonusEnabled", type: "bool", internalType: "bool" },
      {
        name: "_manualContributionEnabled",
        type: "bool",
        internalType: "bool",
      },
      {
        name: "_listingRatePercent",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_startMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_endMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_referralFeeRate",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_buybackPercentage",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_acceptedTokens",
        type: "address[]",
        internalType: "address[]",
      },
      {
        name: "_teamFinanceLock",
        type: "address",
        internalType: "address",
      },
      {
        name: "_uniswapRouter",
        type: "address",
        internalType: "address",
      },
      {
        name: "_nativeTokenPriceFeed",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "isCancelled",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "isFairLaunchActive",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "isFinalized",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "liquidityLockingPaused",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "nativeTokenPriceFeed",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "nextBuybackTime",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "owner",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "prepareAddLiquidity",
    inputs: [
      { name: "", type: "address", internalType: "address" },
      {
        name: "liquidityAmount",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "_finalRate", type: "uint256", internalType: "uint256" },
      { name: "", type: "uint256", internalType: "uint256" },
      { name: "slippage", type: "uint256", internalType: "uint256" },
    ],
    outputs: [
      { name: "tokenAmount", type: "uint256", internalType: "uint256" },
      {
        name: "minTokenAmount",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "minEthAmount",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "deadline", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "prepareLockLiquidity",
    inputs: [
      { name: "lpToken", type: "address", internalType: "address" },
      { name: "amount", type: "uint256", internalType: "uint256" },
      {
        name: "lockDuration",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "owner", type: "address", internalType: "address" },
      {
        name: "factoryAddress",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [
      { name: "receiver", type: "address", internalType: "address" },
      { name: "unlockTime", type: "uint256", internalType: "uint256" },
      { name: "useFactory", type: "bool", internalType: "bool" },
      { name: "factory", type: "address", internalType: "address" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "processReferral",
    inputs: [
      { name: "referralEnabled", type: "bool", internalType: "bool" },
      { name: "referrer", type: "address", internalType: "address" },
      { name: "contributor", type: "address", internalType: "address" },
      { name: "ethValue", type: "uint256", internalType: "uint256" },
      {
        name: "_referralFeeRate",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [
      { name: "referralFee", type: "uint256", internalType: "uint256" },
    ],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "recoverERC20",
    inputs: [
      { name: "token", type: "address", internalType: "address" },
      { name: "amount", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "referralBalance",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "referralCounts",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "referralFeeRate",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "refund",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "renounceOwnership",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setBuybackPercentage",
    inputs: [
      {
        name: "_buybackPercentage",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setBuybackService",
    inputs: [
      {
        name: "_buybackService",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setDefaultReferrer",
    inputs: [{ name: "_referrer", type: "address", internalType: "address" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setManualContributionEnabled",
    inputs: [{ name: "_enabled", type: "bool", internalType: "bool" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setNativeTokenPriceFeed",
    inputs: [{ name: "_priceFeed", type: "address", internalType: "address" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setReferralFeeRate",
    inputs: [{ name: "_newRate", type: "uint256", internalType: "uint256" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setTokensVerified",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "startMultiplier",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "teamFinanceLock",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "totalRaised",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "totalRaisedUSD",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "transferOwnership",
    inputs: [{ name: "newOwner", type: "address", internalType: "address" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "uniswapRouter",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "userInfo",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [
      {
        name: "contribution",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "tokensClaimed",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "tokenAmount", type: "uint256", internalType: "uint256" },
      { name: "refunded", type: "bool", internalType: "bool" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "userTokenContributions",
    inputs: [
      { name: "", type: "address", internalType: "address" },
      { name: "", type: "address", internalType: "address" },
    ],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "validateContributeETH",
    inputs: [
      {
        name: "contribution",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_isFairLaunchActive",
        type: "bool",
        internalType: "bool",
      },
    ],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "validateFairLaunchParameters",
    inputs: [
      {
        name: "totalTokensAllocated",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_listingRatePercent",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "validateMultipliers",
    inputs: [
      {
        name: "_startMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_endMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "validateReferralFeeRate",
    inputs: [
      {
        name: "_referralFeeRate",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [],
    stateMutability: "pure",
  },
  {
    type: "function",
    name: "withdrawAvailableTime",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "withdrawFunds",
    inputs: [
      {
        name: "recipient",
        type: "address",
        internalType: "address payable",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "event",
    name: "BuybackExecuted",
    inputs: [
      {
        name: "ethAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "tokensReceived",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "BuybackPercentageUpdated",
    inputs: [
      {
        name: "newPercentage",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "BuybackServiceUpdated",
    inputs: [
      {
        name: "oldService",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "newService",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ContributionReceived",
    inputs: [
      {
        name: "contributor",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "currentRate",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "DefaultReferrerSet",
    inputs: [
      {
        name: "user",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "referrer",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "DefaultSlippageUpdated",
    inputs: [
      {
        name: "newSlippage",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "EmergencyTokenRecovery",
    inputs: [
      {
        name: "token",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "recipient",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ExcessContributionRefunded",
    inputs: [
      {
        name: "contributor",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "refundAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "FairLaunchCancelled",
    inputs: [],
    anonymous: false,
  },
  {
    type: "event",
    name: "FairLaunchCreated",
    inputs: [
      {
        name: "token",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "startTime",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "endTime",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "totalTokens",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "softCap",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "hardCap",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "FairLaunchFinalized",
    inputs: [
      {
        name: "totalRaised",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "finalRate",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "FundsWithdrawn",
    inputs: [
      {
        name: "recipient",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LiquidityAdded",
    inputs: [
      {
        name: "tokenAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "ethAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "lpAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LiquidityLocked",
    inputs: [
      {
        name: "lpToken",
        type: "address",
        indexed: false,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "unlockTime",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LiquidityLockingToggled",
    inputs: [
      {
        name: "paused",
        type: "bool",
        indexed: false,
        internalType: "bool",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LiquidityNotLocked",
    inputs: [
      {
        name: "lpToken",
        type: "address",
        indexed: false,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ListingRatePercentUpdated",
    inputs: [
      {
        name: "newListingRatePercent",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ManualContributionAdded",
    inputs: [
      {
        name: "contributor",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "referrer",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ManualContributionToggled",
    inputs: [
      {
        name: "enabled",
        type: "bool",
        indexed: false,
        internalType: "bool",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "MultipliersConfigured",
    inputs: [
      {
        name: "startMultiplier",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "endMultiplier",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "NativeTokenPriceFeedUpdated",
    inputs: [
      {
        name: "priceFeed",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "OwnershipTransferred",
    inputs: [
      {
        name: "previousOwner",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "newOwner",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "PartialContributionAccepted",
    inputs: [
      {
        name: "contributor",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "acceptedAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "refundedAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ReferralEarningsClaimed",
    inputs: [
      {
        name: "referrer",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ReferralFeeRateUpdated",
    inputs: [
      {
        name: "newRate",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ReferralPaid",
    inputs: [
      {
        name: "referrer",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ReferralTracked",
    inputs: [
      {
        name: "referrer",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "contributor",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "contributionAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "RefundProcessed",
    inputs: [
      {
        name: "user",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "TokenAcceptanceUpdated",
    inputs: [
      {
        name: "token",
        type: "address",
        indexed: false,
        internalType: "address",
      },
      {
        name: "accepted",
        type: "bool",
        indexed: false,
        internalType: "bool",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "TokenContributionConverted",
    inputs: [
      {
        name: "user",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "token",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "tokenAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "ethValue",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "TokensClaimed",
    inputs: [
      {
        name: "user",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "TokensDeposited",
    inputs: [
      {
        name: "saleToken",
        type: "address",
        indexed: false,
        internalType: "address",
      },
      {
        name: "totalTokensAllocated",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "TokensSwapped",
    inputs: [
      {
        name: "token",
        type: "address",
        indexed: false,
        internalType: "address",
      },
      {
        name: "amountIn",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "ethReceived",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  { type: "error", name: "AddLiquidityFailed", inputs: [] },
  { type: "error", name: "AlreadyClaimed", inputs: [] },
  { type: "error", name: "AlreadyRefunded", inputs: [] },
  { type: "error", name: "BuybackNotEnabled", inputs: [] },
  { type: "error", name: "CannotClaimYet", inputs: [] },
  { type: "error", name: "CannotFinalizeYet", inputs: [] },
  { type: "error", name: "CannotRecoverSaleToken", inputs: [] },
  { type: "error", name: "CannotWithdrawYet", inputs: [] },
  { type: "error", name: "ClaimedReferralFailed", inputs: [] },
  { type: "error", name: "ExceedsHardCap", inputs: [] },
  { type: "error", name: "ExceedsMaxContribution", inputs: [] },
  { type: "error", name: "ExceedsMaxReferralFee", inputs: [] },
  { type: "error", name: "FairLaunchNotActive", inputs: [] },
  { type: "error", name: "InvalidBuybackPercentage", inputs: [] },
  { type: "error", name: "InvalidCaps", inputs: [] },
  { type: "error", name: "InvalidLiquidityPercent", inputs: [] },
  { type: "error", name: "InvalidListingRatePercent", inputs: [] },
  { type: "error", name: "InvalidLockDuration", inputs: [] },
  { type: "error", name: "InvalidMultiplier", inputs: [] },
  { type: "error", name: "InvalidMultiplierOrder", inputs: [] },
  { type: "error", name: "InvalidReferralFee", inputs: [] },
  { type: "error", name: "InvalidReferrer", inputs: [] },
  { type: "error", name: "InvalidState", inputs: [] },
  { type: "error", name: "InvalidSwapPath", inputs: [] },
  { type: "error", name: "InvalidTime", inputs: [] },
  { type: "error", name: "InvalidToken", inputs: [] },
  { type: "error", name: "LockLiquidityFailed", inputs: [] },
  { type: "error", name: "ManualContributionDisabled", inputs: [] },
  { type: "error", name: "NoContribution", inputs: [] },
  { type: "error", name: "NoFundsForBuyback", inputs: [] },
  { type: "error", name: "NoFundsToWithdraw", inputs: [] },
  { type: "error", name: "NoReferralEarnings", inputs: [] },
  { type: "error", name: "NotBuybackService", inputs: [] },
  { type: "error", name: "NotInitialized", inputs: [] },
  { type: "error", name: "NothingToClaim", inputs: [] },
  {
    type: "error",
    name: "OwnableInvalidOwner",
    inputs: [{ name: "owner", type: "address", internalType: "address" }],
  },
  {
    type: "error",
    name: "OwnableUnauthorizedAccount",
    inputs: [{ name: "account", type: "address", internalType: "address" }],
  },
  { type: "error", name: "ReentrancyGuardReentrantCall", inputs: [] },
  { type: "error", name: "ReferralNotEnabled", inputs: [] },
  { type: "error", name: "RefundFailed", inputs: [] },
  { type: "error", name: "RefundsNotEnabled", inputs: [] },
  {
    type: "error",
    name: "SafeERC20FailedOperation",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
  },
  { type: "error", name: "SlippageExceeded", inputs: [] },
  { type: "error", name: "SlippageProtectionRequired", inputs: [] },
  { type: "error", name: "SwapFailed", inputs: [] },
  { type: "error", name: "TeamFinanceNotSet", inputs: [] },
  { type: "error", name: "TokenNotAccepted", inputs: [] },
  { type: "error", name: "TooEarly", inputs: [] },
  { type: "error", name: "TransferFailed", inputs: [] },
  { type: "error", name: "WithdrawFailed", inputs: [] },
  { type: "error", name: "ZeroAddress", inputs: [] },
  { type: "error", name: "ZeroAmount", inputs: [] },
];
