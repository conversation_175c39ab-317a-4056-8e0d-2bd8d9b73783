export const PreSaleABI = [
  { type: "constructor", inputs: [], stateMutability: "nonpayable" },
  { type: "receive", stateMutability: "payable" },
  {
    type: "function",
    name: "WITHDRAW_DELAY",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "acceptedTokens",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "acceptedTokensList",
    inputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "addManualContribution",
    inputs: [
      { name: "contributor", type: "address", internalType: "address" },
      { name: "amount", type: "uint256", internalType: "uint256" },
      { name: "referrer", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "buybackAmount",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "buybackPercentage",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "buybackService",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "address",
        internalType: "contract IBuybackService",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "calculateTokenAmount",
    inputs: [{ name: "ethAmount", type: "uint256", internalType: "uint256" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "cancel",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "claim",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "claimAffiliateRewards",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "configureBonusMultipliers",
    inputs: [
      {
        name: "_startMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_endMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "contributeETH",
    inputs: [{ name: "referrer", type: "address", internalType: "address" }],
    outputs: [],
    stateMutability: "payable",
  },
  {
    type: "function",
    name: "contributeTokens",
    inputs: [
      { name: "token", type: "address", internalType: "address" },
      { name: "amount", type: "uint256", internalType: "uint256" },
      { name: "referrer", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "defaultReferrer",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "endMultiplier",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "executeBuyback",
    inputs: [
      { name: "minTokensOut", type: "uint256", internalType: "uint256" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "feePercentage",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "feeRecipient",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "feeThreshold",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "finalize",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "getFullState",
    inputs: [],
    outputs: [
      {
        name: "",
        type: "tuple",
        internalType: "struct PresaleLib.PresaleFullState",
        components: [
          {
            name: "info",
            type: "tuple",
            internalType: "struct PresaleLib.PresaleInfo",
            components: [
              {
                name: "saleToken",
                type: "address",
                internalType: "address",
              },
              {
                name: "softCap",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "hardCap",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "presaleRate",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "listingRate",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "liquidityPercent",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "lockupDuration",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "startTime",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "endTime",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "buybackEnabled",
                type: "bool",
                internalType: "bool",
              },
              {
                name: "referralEnabled",
                type: "bool",
                internalType: "bool",
              },
              {
                name: "bonusEnabled",
                type: "bool",
                internalType: "bool",
              },
              {
                name: "manualContributionEnabled",
                type: "bool",
                internalType: "bool",
              },
            ],
          },
          {
            name: "config",
            type: "tuple",
            internalType: "struct PresaleLib.PresaleConfig",
            components: [
              {
                name: "startMultiplier",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "endMultiplier",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "referralFeeRate",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "buybackPercentage",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "liquiditySlippageTolerance",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "swapSlippageTolerance",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "initialized",
                type: "bool",
                internalType: "bool",
              },
              {
                name: "featuresInitialized",
                type: "bool",
                internalType: "bool",
              },
            ],
          },
          {
            name: "progress",
            type: "tuple",
            internalType: "struct PresaleLib.PresaleProgress",
            components: [
              {
                name: "totalRaised",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "totalTokensSold",
                type: "uint256",
                internalType: "uint256",
              },
              { name: "isActive", type: "bool", internalType: "bool" },
              {
                name: "isFinalized",
                type: "bool",
                internalType: "bool",
              },
              {
                name: "isCancelled",
                type: "bool",
                internalType: "bool",
              },
              {
                name: "buybackAmount",
                type: "uint256",
                internalType: "uint256",
              },
              {
                name: "nextBuybackTime",
                type: "uint256",
                internalType: "uint256",
              },
            ],
          },
          {
            name: "addresses",
            type: "tuple",
            internalType: "struct PresaleLib.PresaleAddresses",
            components: [
              {
                name: "teamFinanceLock",
                type: "address",
                internalType: "address",
              },
              {
                name: "uniswapRouter",
                type: "address",
                internalType: "address",
              },
              {
                name: "buybackService",
                type: "address",
                internalType: "address",
              },
            ],
          },
          {
            name: "tokens",
            type: "tuple",
            internalType: "struct PresaleLib.PresaleTokenInfo",
            components: [
              {
                name: "saleToken",
                type: "address",
                internalType: "address",
              },
              {
                name: "acceptedTokens",
                type: "address[]",
                internalType: "address[]",
              },
            ],
          },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getHasRequiredTokens",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getIsFinalized",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getLatestETHPrice",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getSoftCap",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getTimeUntilOwnerWithdrawal",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getTotalRaised",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getTotalRequiredTokens",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getUserInfo",
    inputs: [{ name: "user", type: "address", internalType: "address" }],
    outputs: [
      {
        name: "contribution",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "tokensClaimed",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "hasRefunded", type: "bool", internalType: "bool" },
      {
        name: "referralEarnings",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "getUserInfoState",
    inputs: [{ name: "user", type: "address", internalType: "address" }],
    outputs: [
      {
        name: "",
        type: "tuple",
        internalType: "struct PresaleLib.UserInfoState",
        components: [
          {
            name: "contribution",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "tokensAllocated",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "tokensClaimed",
            type: "uint256",
            internalType: "uint256",
          },
          { name: "hasRefunded", type: "bool", internalType: "bool" },
          {
            name: "referralEarnings",
            type: "uint256",
            internalType: "uint256",
          },
          {
            name: "referralCount",
            type: "uint256",
            internalType: "uint256",
          },
        ],
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "hasEnoughTokensForSale",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "initialize",
    inputs: [
      { name: "_saleToken", type: "address", internalType: "address" },
      { name: "_softCap", type: "uint256", internalType: "uint256" },
      { name: "_hardCap", type: "uint256", internalType: "uint256" },
      {
        name: "_presaleRate",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_listingRate",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_liquidityPercent",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_lockupDuration",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "_startTime", type: "uint256", internalType: "uint256" },
      { name: "_endTime", type: "uint256", internalType: "uint256" },
      { name: "_creator", type: "address", internalType: "address" },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "initializeFeatures",
    inputs: [
      { name: "_buybackEnabled", type: "bool", internalType: "bool" },
      { name: "_referralEnabled", type: "bool", internalType: "bool" },
      { name: "_bonusEnabled", type: "bool", internalType: "bool" },
      {
        name: "_manualContributionEnabled",
        type: "bool",
        internalType: "bool",
      },
      {
        name: "_startMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_endMultiplier",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_referralFeeRate",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_buybackPercentage",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_acceptedTokens",
        type: "address[]",
        internalType: "address[]",
      },
      {
        name: "_teamFinanceLock",
        type: "address",
        internalType: "address",
      },
      {
        name: "_uniswapRouter",
        type: "address",
        internalType: "address",
      },
      {
        name: "_nativeTokenPriceFeed",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "isCancelled",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "isFinalized",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "isPresaleActive",
    inputs: [],
    outputs: [{ name: "", type: "bool", internalType: "bool" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "liquiditySlippageTolerance",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "nativeTokenPriceFeed",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "nextBuybackTime",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "owner",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "presaleInfo",
    inputs: [],
    outputs: [
      { name: "saleToken", type: "address", internalType: "address" },
      { name: "softCap", type: "uint256", internalType: "uint256" },
      { name: "hardCap", type: "uint256", internalType: "uint256" },
      { name: "presaleRate", type: "uint256", internalType: "uint256" },
      { name: "listingRate", type: "uint256", internalType: "uint256" },
      {
        name: "liquidityPercent",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "lockupDuration",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "startTime", type: "uint256", internalType: "uint256" },
      { name: "endTime", type: "uint256", internalType: "uint256" },
      { name: "buybackEnabled", type: "bool", internalType: "bool" },
      { name: "referralEnabled", type: "bool", internalType: "bool" },
      { name: "bonusEnabled", type: "bool", internalType: "bool" },
      {
        name: "manualContributionEnabled",
        type: "bool",
        internalType: "bool",
      },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "referralBalance",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "referralCounts",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "referralFeeRate",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "refund",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "renounceOwnership",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "requiredLiquidityTokens",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "requiredPresaleTokens",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "setBuybackPercentage",
    inputs: [
      {
        name: "_buybackPercentage",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setBuybackService",
    inputs: [
      {
        name: "_buybackService",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setDefaultReferrer",
    inputs: [{ name: "_referrer", type: "address", internalType: "address" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setFeeParameters",
    inputs: [
      {
        name: "_feeThreshold",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_feePercentage",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "_feeRecipient",
        type: "address",
        internalType: "address",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setLiquiditySlippageTolerance",
    inputs: [
      {
        name: "_slippageTolerance",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setManualContributionEnabled",
    inputs: [{ name: "_enabled", type: "bool", internalType: "bool" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setNativeTokenPriceFeed",
    inputs: [{ name: "_priceFeed", type: "address", internalType: "address" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setReferralFeeRate",
    inputs: [
      {
        name: "_referralFeeRate",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setSwapSlippageTolerance",
    inputs: [
      {
        name: "_slippageTolerance",
        type: "uint256",
        internalType: "uint256",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "setTokensVerified",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "startMultiplier",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "swapSlippageTolerance",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "teamFinanceLock",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "tokenContributions",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "totalRaised",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "totalRaisedUSD",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "totalTokensDistributed",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "totalTokensSold",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "transferOwnership",
    inputs: [{ name: "newOwner", type: "address", internalType: "address" }],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "uniswapRouter",
    inputs: [],
    outputs: [{ name: "", type: "address", internalType: "address" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "userInfo",
    inputs: [{ name: "", type: "address", internalType: "address" }],
    outputs: [
      {
        name: "contribution",
        type: "uint256",
        internalType: "uint256",
      },
      {
        name: "tokensClaimed",
        type: "uint256",
        internalType: "uint256",
      },
      { name: "tokenAmount", type: "uint256", internalType: "uint256" },
      { name: "refunded", type: "bool", internalType: "bool" },
    ],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "verifyAndSetRequiredTokens",
    inputs: [],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "function",
    name: "withdrawAvailableTime",
    inputs: [],
    outputs: [{ name: "", type: "uint256", internalType: "uint256" }],
    stateMutability: "view",
  },
  {
    type: "function",
    name: "withdrawFunds",
    inputs: [
      {
        name: "recipient",
        type: "address",
        internalType: "address payable",
      },
    ],
    outputs: [],
    stateMutability: "nonpayable",
  },
  {
    type: "event",
    name: "BuybackExecuted",
    inputs: [
      {
        name: "ethAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "tokensReceived",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "BuybackPercentageUpdated",
    inputs: [
      {
        name: "newPercentage",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "BuybackServiceUpdated",
    inputs: [
      {
        name: "buybackService",
        type: "address",
        indexed: false,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "DefaultReferrerSet",
    inputs: [
      {
        name: "user",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "referrer",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ExcessContributionRefunded",
    inputs: [
      {
        name: "contributor",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "refundAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "FeePaid",
    inputs: [
      {
        name: "recipient",
        type: "address",
        indexed: false,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "FeeParametersUpdated",
    inputs: [
      {
        name: "threshold",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "percentage",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "recipient",
        type: "address",
        indexed: false,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "FundsWithdrawn",
    inputs: [
      {
        name: "recipient",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "InitializationAttempt",
    inputs: [
      {
        name: "caller",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "saleToken",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "initialized",
        type: "bool",
        indexed: false,
        internalType: "bool",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "InitializationError",
    inputs: [
      {
        name: "reason",
        type: "string",
        indexed: false,
        internalType: "string",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LiquidityAdded",
    inputs: [
      {
        name: "tokenAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "ethAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "lpAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LiquidityLocked",
    inputs: [
      {
        name: "lpToken",
        type: "address",
        indexed: false,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "unlockTime",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "LiquiditySlippageToleranceUpdated",
    inputs: [
      {
        name: "newTolerance",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ManualContributionAdded",
    inputs: [
      {
        name: "contributor",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "referrer",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ManualContributionToggled",
    inputs: [
      {
        name: "enabled",
        type: "bool",
        indexed: false,
        internalType: "bool",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "MultipliersConfigured",
    inputs: [
      {
        name: "startMultiplier",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "endMultiplier",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "NativeTokenPriceFeedUpdated",
    inputs: [
      {
        name: "priceFeed",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "OwnershipTransferred",
    inputs: [
      {
        name: "previousOwner",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "newOwner",
        type: "address",
        indexed: true,
        internalType: "address",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "PresaleCancelled",
    inputs: [],
    anonymous: false,
  },
  {
    type: "event",
    name: "PresaleCreated",
    inputs: [
      {
        name: "token",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "startTime",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "endTime",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "PresaleFinalized",
    inputs: [
      {
        name: "totalRaised",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "totalTokens",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ReferralEarningsClaimed",
    inputs: [
      {
        name: "referrer",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ReferralFeeRateUpdated",
    inputs: [
      {
        name: "newRate",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ReferralPaid",
    inputs: [
      {
        name: "referrer",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "ReferralTracked",
    inputs: [
      {
        name: "referrer",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "contributor",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "contributionAmount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "RefundProcessed",
    inputs: [
      {
        name: "user",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "RequiredTokensVerified",
    inputs: [
      {
        name: "totalTokensRequired",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "SwapSlippageToleranceUpdated",
    inputs: [
      {
        name: "newTolerance",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "TokensPurchased",
    inputs: [
      {
        name: "buyer",
        type: "address",
        indexed: true,
        internalType: "address",
      },
      {
        name: "amount",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "tokens",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  {
    type: "event",
    name: "TokensSwapped",
    inputs: [
      {
        name: "token",
        type: "address",
        indexed: false,
        internalType: "address",
      },
      {
        name: "amountIn",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
      {
        name: "ethReceived",
        type: "uint256",
        indexed: false,
        internalType: "uint256",
      },
    ],
    anonymous: false,
  },
  { type: "error", name: "AddLiquidityFailed", inputs: [] },
  { type: "error", name: "AlreadyClaimed", inputs: [] },
  { type: "error", name: "AlreadyRefunded", inputs: [] },
  { type: "error", name: "BuybackNotConfigured", inputs: [] },
  { type: "error", name: "BuybackNotEnabled", inputs: [] },
  { type: "error", name: "CannotClaimYet", inputs: [] },
  { type: "error", name: "CannotFinalizeYet", inputs: [] },
  { type: "error", name: "CannotWithdrawYet", inputs: [] },
  { type: "error", name: "ClaimedReferralFailed", inputs: [] },
  { type: "error", name: "ExceedsHardCap", inputs: [] },
  { type: "error", name: "InvalidBuybackAmount", inputs: [] },
  { type: "error", name: "InvalidBuybackPercentage", inputs: [] },
  { type: "error", name: "InvalidBuybackService", inputs: [] },
  { type: "error", name: "InvalidCaps", inputs: [] },
  { type: "error", name: "InvalidLiquidityPercent", inputs: [] },
  { type: "error", name: "InvalidLockDuration", inputs: [] },
  { type: "error", name: "InvalidMultiplier", inputs: [] },
  { type: "error", name: "InvalidMultiplierOrder", inputs: [] },
  { type: "error", name: "InvalidReferralFee", inputs: [] },
  { type: "error", name: "InvalidReferrer", inputs: [] },
  { type: "error", name: "InvalidState", inputs: [] },
  { type: "error", name: "InvalidSwapPath", inputs: [] },
  { type: "error", name: "InvalidTime", inputs: [] },
  { type: "error", name: "InvalidToken", inputs: [] },
  { type: "error", name: "LockLiquidityFailed", inputs: [] },
  { type: "error", name: "ManualContributionDisabled", inputs: [] },
  { type: "error", name: "NoContribution", inputs: [] },
  { type: "error", name: "NoFundsForBuyback", inputs: [] },
  { type: "error", name: "NoFundsToWithdraw", inputs: [] },
  { type: "error", name: "NoReferralEarnings", inputs: [] },
  { type: "error", name: "NotBuybackService", inputs: [] },
  { type: "error", name: "NotInitialized", inputs: [] },
  { type: "error", name: "NothingToClaim", inputs: [] },
  {
    type: "error",
    name: "OwnableInvalidOwner",
    inputs: [{ name: "owner", type: "address", internalType: "address" }],
  },
  {
    type: "error",
    name: "OwnableUnauthorizedAccount",
    inputs: [{ name: "account", type: "address", internalType: "address" }],
  },
  { type: "error", name: "PresaleNotActive", inputs: [] },
  { type: "error", name: "ReentrancyGuardReentrantCall", inputs: [] },
  { type: "error", name: "ReferralNotEnabled", inputs: [] },
  { type: "error", name: "RefundFailed", inputs: [] },
  { type: "error", name: "RefundsNotEnabled", inputs: [] },
  {
    type: "error",
    name: "SafeERC20FailedOperation",
    inputs: [{ name: "token", type: "address", internalType: "address" }],
  },
  { type: "error", name: "SwapFailed", inputs: [] },
  { type: "error", name: "TeamFinanceNotSet", inputs: [] },
  { type: "error", name: "TokenNotAccepted", inputs: [] },
  { type: "error", name: "TooEarly", inputs: [] },
  { type: "error", name: "TransferFailed", inputs: [] },
  { type: "error", name: "WithdrawFailed", inputs: [] },
  { type: "error", name: "ZeroAddress", inputs: [] },
  { type: "error", name: "ZeroAmount", inputs: [] },
];
