import {
  createPublicClient,
  createWalletClient,
  custom,
  http,
  PublicClient,
  WalletClient,
} from "viem";
import { chains } from "./config";
import { Provider } from "@/constants/types";

export async function getEthereumConfig(chainId: number, provider: Provider) {
  try {
    const chain = chains[chainId];
    if (!chain) {
      throw new Error(`Chain with ID ${chainId} is not supported`);
    }

    if (!provider) {
      throw new Error("No ethereum provider found. Please install a wallet.");
    }

    const walletClient = createWalletClient({
      chain: chain.config,
      transport: custom(provider),
    });

    const publicClient = createPublicClient({
      chain: chain.config,
      transport: http(),
    });

    const [account] = await walletClient.getAddresses();
    if (!account) {
      throw new Error("No account found");
    }

    return {
      walletClient: walletClient as WalletClient,
      publicClient: publicClient as PublicClient,
      chain,
      account,
    };
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred";
    throw new Error(`Failed to configure Ethereum: ${errorMessage}`);
  }
}
