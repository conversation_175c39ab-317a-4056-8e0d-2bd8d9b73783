import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
import Cookies from "js-cookie";
import { isTokenExpired } from "@/utils/tokenChecker";
import { User } from "@/constants/types";

interface States {
  user: User | null;
  isInitialized: boolean;
  isLoggedIn: boolean;
  isLoggingOut: boolean;
}

interface Actions {
  setUser: (user: User | null) => void;
  initializeAuth: (user: User | null | undefined) => void;
  setInitialized: (initialized: boolean) => void;
  checkToken: () => Promise<boolean>;
  setIsLoggedIn: (state: boolean) => void;
  setIsLoggingOut: (state: boolean) => void;
  logout: () => void;
}

const useUserStore = create(
  persist<States & Actions>(
    (set, get) => ({
      user: null,
      isLoggedIn: false,
      isInitialized: false,
      isLoggingOut: false,
      setUser: (user: User | null | undefined) => set({ user }),
      setInitialized: (initialized: boolean) =>
        set({ isInitialized: initialized }),
      setIsLoggedIn: (state: boolean) => set({ isLoggedIn: state }),
      setIsLoggingOut: (state: boolean) => set({ isLoggingOut: state }),
      logout: () => {
        // Clear authentication cookie
        Cookies.remove("Authentication");
        // Reset user state
        set({
          user: null,
          isLoggedIn: false,
          isLoggingOut: false,
        });
      },
      initializeAuth: async (user: User | null | undefined) => {
        const token = Cookies.get("Authentication");
        if (token && user) {
          const isValid = await get().checkToken();
          if (isValid) {
            set({ user, isLoggedIn: true, isInitialized: true });
          } else {
            set({ user: null, isLoggedIn: false, isInitialized: true });
          }
        } else {
          set({ user: null, isLoggedIn: false, isInitialized: true });
        }
      },

      checkToken: async () => {
        const token = Cookies.get("Authentication");

        if (!token) {
          set({ isLoggedIn: false, user: null });
          return false;
        }

        try {
          const tokenExpired = isTokenExpired(token);

          if (tokenExpired) {
            Cookies.remove("Authentication");
            set({ isLoggedIn: false, user: null });
            return false;
          } else {
            set({ isLoggedIn: true });
            return true;
          }
        } catch (error) {
          console.error("Error checking token:", error);
          set({ isLoggedIn: false, user: null });
          return false;
        }
      },
    }),
    {
      name: "user",
      storage: createJSONStorage(() => localStorage),
    }
  )
);

export default useUserStore;
