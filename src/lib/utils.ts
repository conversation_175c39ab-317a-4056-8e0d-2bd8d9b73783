import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Formats a number or numeric string using the `Intl.NumberFormat` API.
 *
 * @param value - The numeric value or string to format. If a string is provided, it will be parsed into a number.
 *                If the value cannot be parsed into a number, it will be returned as-is.
 * @param options - Optional formatting options to customize the output. These options are merged with
 *                  the default options, which include a maximum of 2 fraction digits.
 * @returns The formatted number as a string if the input is a valid number, or the original value if it is not.
 *
 * @example
 * ```typescript
 * formatNumber(1234.567); // "1,234.57"
 * formatNumber("1234.567", { minimumFractionDigits: 3 }); // "1,234.567"
 * formatNumber("abc"); // "abc"
 * ```
 */
export function formatNumber(
  value: string | number,
  options?: Intl.NumberFormatOptions
) {
  const parseValue = typeof value === "string" ? parseFloat(value) : value;
  if (isNaN(parseValue)) {
    return value;
  }
  if (typeof value === "number") {
    return new Intl.NumberFormat("en-US", {
      maximumFractionDigits: 2,
      ...options,
    }).format(value);
  }
  return value;
}

/**
 * Formats a given value as a currency string in USD.
 *
 * @param value - The numeric value or string representation of a number to format.
 * @param options - Optional formatting options to customize the output. These options
 *                  are merged with the default options, which include `style: "currency"`
 *                  and `currency: "USD"`.
 * @returns The formatted currency string.
 *
 * @example
 * ```typescript
 * formatCurrency(1234.567); // "$1,234.57"
 * formatCurrency("1234.567", { minimumFractionDigits: 3 }); // "$1,234.567"
 * formatCurrency("abc"); // "abc"
 * ```
 */
export function formatCurrency(
  value: string | number,
  options?: Intl.NumberFormatOptions
) {
  return formatNumber(value, {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 0,
    ...options,
  });
}
