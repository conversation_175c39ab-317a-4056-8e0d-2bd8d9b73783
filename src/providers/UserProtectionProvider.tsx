"use client";

import { useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import useUserStore from "@/store/user.store";
import { useAccount } from "wagmi";

export function UserProtectionProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isInitialized, isLoggedIn } = useUserStore();
  const router = useRouter();
  const pathname = usePathname();
  const { isConnected } = useAccount();

  useEffect(() => {
    if (isInitialized) {
      if (!isLoggedIn || !isConnected) {
        router.push("/");
      }
    }
  }, [router, pathname, isInitialized, isLoggedIn, isConnected]);

  if (!isInitialized) {
    return null;
  }

  return <>{children}</>;
}
