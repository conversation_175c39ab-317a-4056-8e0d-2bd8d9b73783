"use client";

import "@rainbow-me/rainbowkit/styles.css";

import { getDefaultConfig, RainbowKitProvider } from "@rainbow-me/rainbowkit";
import {
  metaMaskWallet,
  coinbaseWallet,
  trustWallet,
} from "@rainbow-me/rainbowkit/wallets";
import { WagmiProvider } from "wagmi";
import { base } from "wagmi/chains";
import QueryProvider from "./QueryProvider";

const walletList = [
  {
    groupName: "Recommended",
    wallets: [metaMaskWallet, coinbaseWallet, trustWallet],
  },
];

// Create config just ONCE outside the component
export const config = getDefaultConfig({
  appName: "Capitalize",
  projectId: process.env.NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID!,
  chains: [base],
  wallets: walletList,
  ssr: false,
});

const EvmWalletProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <WagmiProvider config={config}>
      <QueryProvider>
        <RainbowKitProvider initialChain={base} showRecentTransactions={true}>
          {children}
        </RainbowKitProvider>
      </QueryProvider>
    </WagmiProvider>
  );
};

export default EvmWalletProvider;
