"use client";

import { useGetAuthenticatedUser } from "@/api/user/user.queries";
import useUserStore from "@/store/user.store";
import { usePathname } from "next/navigation";
import { useEffect } from "react";

interface ApiError {
  response?: {
    status: number;
  };
}

const UserProvider = ({ children }: { children: React.ReactNode }) => {
  const { initializeAuth, isInitialized, checkToken, setInitialized } =
    useUserStore();
  const pathname = usePathname();

  // Initialize query in background without blocking
  const { user, isSuccess, error } = useGetAuthenticatedUser();

  const isApiError = (error: unknown): error is ApiError => {
    return error !== null && typeof error === "object" && "response" in error;
  };

  useEffect(() => {
    checkToken();
    if (isSuccess) {
      initializeAuth(user);
    } else if (error && isApiError(error) && error.response?.status === 401) {
      initializeAuth(null);
    }
    setInitialized(true);
  }, [
    initializeAuth,
    user,
    isSuccess,
    error,
    isInitialized,
    pathname,
    checkToken,
    setInitialized,
  ]);

  if (!isInitialized) {
    return <div className="">Loading...</div>;
  }

  return <div className="">{children}</div>;
};

export default UserProvider;
