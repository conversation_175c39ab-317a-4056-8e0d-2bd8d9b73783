import axios, { AxiosRequestConfig, AxiosResponse } from "axios";
import Cookies from "js-cookie";

const api = process.env.NEXT_PUBLIC_BACKEND_API;

export const client = axios.create({
  baseURL: api,
});

export const request = <T = unknown>(
  options: AxiosRequestConfig
): Promise<AxiosResponse<T>> => {
  const token = Cookies.get("Authentication");

  return client({
    ...options,
    headers: {
      ...options.headers,
      Authorization: token ? `Bearer ${token}` : "",
    },
    withCredentials: true,
  });
};

export const removeHeaderToken = (): void => {
  delete client.defaults.headers.common["Authorization"];
};
