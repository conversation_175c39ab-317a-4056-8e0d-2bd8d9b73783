import { formatCurrency, formatNumber } from "@/lib/utils";
import Image from "next/image";

type OrderProps = {
  order: {
    id: number;
    value: number;
    status: string;
  };
};

const Order: React.FC<OrderProps> = ({ order }) => {
  return (
    <div className="flex items-center justify-between gap-[0.62rem]">
      <div className="flex items-center gap-[0.62rem]">
        <div className="flex flex-col items-center justify-center text-palette-green">
          <Image
            src="/icons/arrow-down.svg"
            alt="Arrow Right"
            width={24}
            height={24}
            className="size-3"
          />
          <span className="text-xxs">Buy</span>
        </div>
        <div className="flex flex-col">
          <p className="font-bold">{formatNumber(order.value)} USDP</p>
          <p className="text-xs text-typography-400">
            ≈ {formatCurrency(order.value)} <strong>{order.status}</strong>
          </p>
        </div>
      </div>
      <div>
        <div className="flex items-center justify-end gap-2 text-xxs text-typography-200">
          <button
            aria-label="View Order Details"
            type="button"
            className="flex items-center justify-center gap-1"
          >
            <Image
              src="/icons/search.svg"
              alt="View Order Details"
              width={24}
              height={24}
              className="size-[0.69844rem]"
            />
            <span>Detailed View</span>
          </button>
          {order.status === "pending" && (
            <button
              aria-label="View Order Details"
              type="button"
              className="flex items-center justify-center gap-1"
            >
              <Image
                src="/icons/close.svg"
                alt="Cancel Order"
                width={24}
                height={24}
                className="size-[0.69844rem]"
              />
              <span>Cancel Order</span>
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default Order;
