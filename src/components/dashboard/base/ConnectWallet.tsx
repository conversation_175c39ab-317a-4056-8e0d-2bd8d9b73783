"use client";
import { useAccount, useDisconnect } from "wagmi";
import { useCallback, useEffect, useState } from "react";
import { AxiosError, AxiosResponse } from "axios";
import { ErrorResponse } from "@/api/type";
import { RConnectWallet } from "@/api/auth/auth.types";
import toast from "react-hot-toast";
import Cookies from "js-cookie";
import { useConnectWallet } from "@/api/auth/auth.queries";
import useUserStore from "@/store/user.store";
import { getWalletNonceRequest } from "@/api/user/user.apis";
import { Provider } from "@/constants/types";
import { getEthereumConfig } from "@/services/blockchain";
import { chains } from "@/services/config";
import { Loader2, Wallet } from "lucide-react";
import Button from "@/components/ui/button";
import { ConnectButton } from "@rainbow-me/rainbowkit";

const ConnectWallet = () => {
  const { initializeAuth } = useUserStore();
  const { disconnect } = useDisconnect();

  const { address, isConnected, chainId, connector } = useAccount();
  const [isCorrectNetwork, setIsCorrectNetwork] = useState(true);
  const [authLoading, setAuthLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);

  console.log(isCorrectNetwork);

  useEffect(() => {
    if (errorMsg) {
      const timer = setTimeout(() => {
        setErrorMsg(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [errorMsg]);

  const onError = (error: AxiosError<ErrorResponse>) => {
    const errorMessage = error?.response?.data?.message;
    const descriptions = Array.isArray(errorMessage)
      ? errorMessage
      : [errorMessage];

    toast.error(descriptions[0] || "An error occurred");
    setErrorMsg(descriptions[0] || "An error occurred");
    setAuthLoading(false);
  };

  const onSuccess = async (data: AxiosResponse<RConnectWallet>) => {
    const token = data.data.data.token;
    const user = data.data.data.user;
    Cookies.set("Authentication", token);
    initializeAuth(user);
    toast.success("Identity verified successfully!");
    setAuthLoading(false);
    setErrorMsg(null);
  };

  const { mutate: connectWallet } = useConnectWallet(onError, onSuccess);

  const handleDisconnect = async () => {
    disconnect();
    try {
      if (window.ethereum && window.ethereum.request) {
        await window.ethereum.request({
          method: "wallet_revokePermissions",
          params: [{ eth_accounts: {} }],
        });
        if (window.localStorage) {
          Object.keys(window.localStorage)
            .filter((key) => key.startsWith("wagmi") || key.includes("wallet"))
            .forEach((key) => window.localStorage.removeItem(key));
        }
      }
    } catch (error) {
      console.log(error);
      // ignore
    }
  };

  const handleAuthenticate = useCallback(async () => {
    setAuthLoading(true);
    setErrorMsg(null);
    try {
      const provider = (await connector?.getProvider()) as Provider;
      if (!isConnected || !address || !chainId) return;
      const { walletClient, account } = await getEthereumConfig(
        chainId,
        provider
      );
      const { data } = await getWalletNonceRequest({
        walletAddress: address,
      });
      const nonce = data?.data;
      const message = `Login to Usdp with nonce: ${nonce}`;
      const signature = await walletClient.signMessage({
        account,
        message,
      });
      connectWallet({
        walletAddress: address,
        signature,
        chainId,
      });
    } catch (error) {
      setAuthLoading(false);
      const errorMessage =
        error instanceof Error ? error.message : "An unknown error occurred";
      setErrorMsg(errorMessage);
      toast.error(errorMessage);
    }
  }, [address, chainId, connectWallet, connector, isConnected]);

  useEffect(() => {
    if (isConnected && chainId) {
      setIsCorrectNetwork(!!chains[chainId]);
    }
  }, [isConnected, chainId]);

  // REMOVED: The auto-authentication useEffect that was causing automatic connection attempts
  // This was the problematic code that auto-triggered authentication:
  /*
  useEffect(() => {
    if (
      isInitialized &&
      isConnected &&
      address &&
      isCorrectNetwork &&
      !isLoggedIn &&
      !user &&
      !isLoggingOut
    ) {
      handleAuthenticate();
    }
  }, [
    isConnected,
    address,
    isCorrectNetwork,
    handleAuthenticate,
    isLoggedIn,
    isLoggingOut,
    chainId,
    user,
    isInitialized,
  ]);
  */

  return (
    <div className="flex flex-col gap-4 text-white items-center justify-center min-h-[60vh] px-4">
      <div className="flex flex-col gap-4 rounded-xl relative px-4 pb-6 pt-6 w-full max-w-sm mx-auto bg-white/10 backdrop-blur-md shadow-lg">
        <div className="flex flex-col items-center gap-2">
          <div className="rounded-full bg-[#B6A990] w-14 h-14 flex items-center justify-center mb-2">
            <Wallet className="w-8 h-8 text-[#2E7D51]" />
          </div>
          <h1 className="text-2xl font-bold text-center">
            {isConnected ? "Authenticate your wallet" : "Connect your wallet"}
          </h1>
          <p className="text-sm text-center text-white/80">
            {isConnected
              ? "Please authenticate your wallet to access your dashboard."
              : "To access your dashboard, please connect and authenticate your wallet."}
          </p>
        </div>
        {errorMsg && (
          <div className="bg-red-500/80 text-white text-sm rounded-lg px-3 py-2 text-center">
            {errorMsg}
          </div>
        )}
        <div className="flex flex-col gap-2 mt-2">
          <ConnectButton.Custom>
            {({
              account,
              chain,
              openChainModal,
              openConnectModal,
              authenticationStatus,
              mounted,
            }) => {
              const ready = mounted && authenticationStatus !== "loading";
              const connected =
                ready &&
                account &&
                chain &&
                (!authenticationStatus ||
                  authenticationStatus === "authenticated");

              if (!mounted) {
                return null;
              }

              if (!connected) {
                return (
                  <Button
                    className="w-full py-3 rounded-xl bg-gradient-to-r from-[#ACEDC9] to-[#2E7D51] text-white font-bold text-lg flex items-center justify-center gap-2 shadow-md"
                    onClick={openConnectModal}
                    disabled={authLoading}
                    type="button"
                  >
                    {authLoading ? (
                      <Loader2 className="animate-spin w-6 h-6" />
                    ) : (
                      <>
                        <Wallet className="w-6 h-6" /> Connect Wallet
                      </>
                    )}
                  </Button>
                );
              }

              if (chain.unsupported) {
                return (
                  <Button
                    className="w-full py-3 rounded-xl bg-red-600 text-white font-bold text-lg flex items-center justify-center gap-2 shadow-md"
                    onClick={openChainModal}
                    type="button"
                  >
                    Wrong network
                  </Button>
                );
              }

              // If connected and on supported network, show Authenticate and Disconnect
              return (
                <div className="flex flex-col gap-2">
                  <Button
                    className="w-full py-3 rounded-xl bg-gradient-to-r from-[#ACEDC9] to-[#2E7D51] text-white font-bold text-lg flex items-center justify-center gap-2 shadow-md"
                    onClick={handleAuthenticate}
                    disabled={authLoading}
                    type="button"
                  >
                    {authLoading ? (
                      <Loader2 className="animate-spin w-6 h-6" />
                    ) : (
                      <>Authenticate</>
                    )}
                  </Button>
                  <Button
                    className="bg-[#A5A5A5] "
                    onClick={handleDisconnect}
                    disabled={authLoading}
                    type="button"
                  >
                    <p className="text-lg font-bold">Disconnect</p>
                  </Button>
                </div>
              );
            }}
          </ConnectButton.Custom>
        </div>
      </div>
    </div>
  );
};

export default ConnectWallet;
