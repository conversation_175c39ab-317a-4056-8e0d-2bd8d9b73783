"use client";
import useUserStore from "@/store/user.store";
import Balance from "./balance";
import Orders from "./orders";
import UserInfo from "./user-info";
import { useAccount } from "wagmi";
import ConnectWallet from "./ConnectWallet";

const DashboardBase = () => {
  const { isLoggedIn, user } = useUserStore();

  const { isConnected, address, chainId } = useAccount();

  const connected =
    isConnected &&
    isLoggedIn &&
    address?.toLowerCase() == user?.walletAddress?.toLowerCase() &&
    chainId === user?.chainId;

  return (
    <>
      {connected ? (
        <div className="flex flex-col gap-3 overflow-y-auto h-screen">
          <section>
            <UserInfo />
          </section>
          <section className="mt-6">
            <Balance />
          </section>
          <section className="mb-56">
            <Orders />
          </section>
        </div>
      ) : (
        <ConnectWallet />
      )}
    </>
  );
};

export default DashboardBase;
