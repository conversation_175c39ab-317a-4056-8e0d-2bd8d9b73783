import { Card, CardHeader } from "@/components/ui/card";
import Image from "next/image";
import Order from "./order";

const demo = [
  {
    id: 1,
    value: 123456,
    status: "pending",
  },
  {
    id: 2,
    value: 5154,
    status: "pending",
  },
  {
    id: 3,
    value: 7890,
    status: "completed",
  },
];

const Orders = () => {
  return (
    <Card>
      <CardHeader className="p-0">
        <div className="flex items-center justify-between gap-[0.62rem] border-b pb-2">
          <h2 className="font-bold">My Orders</h2>
          <button aria-label="Menu" type="button">
            <Image
              src="/icons/three-dots.svg"
              alt="Arrow Right"
              width={24}
              height={24}
              className="size-5"
            />
          </button>
        </div>
        <div className="space-y-3.5">
          {demo.map((order) => (
            <Order key={order.id} order={order} />
          ))}
        </div>
      </CardHeader>
    </Card>
  );
};

export default Orders;
