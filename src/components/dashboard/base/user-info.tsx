import { Card } from "@/components/ui/card";
import Image from "next/image";

const UserInfo = () => {
  return (
    <Card>
      <div className="flex items-center justify-between gap-[0.62rem]">
        <p className="font-black"><PERSON></p>
        <div className="flex items-center text-xs justify-end gap-[0.62rem]">
          <p>
            Private ID <strong>546779</strong>
          </p>
          <div className="flex items-center gap-1.5">
            <Image
              src="/icons/money-bag.svg"
              alt="Money Bag"
              width={24}
              height={24}
              className="size-[0.73656rem]"
            />
            <span>Smart Wallet</span>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default UserInfo;
