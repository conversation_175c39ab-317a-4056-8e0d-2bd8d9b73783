"use client";
import { Card } from "@/components/ui/card";
import UserInfo from "../base/user-info";
import Hero from "./hero";
import { Star } from "lucide-react";
import Image from "next/image";
import images from "@/public/images";
import { useState } from "react";
import ReviewForm from "./review-form";

const Reviews = () => {
  const [reviewForm, setReviewForm] = useState(false);

  return (
    <div className="space-y-3 overflow-y-auto h-[calc(100vh-1rem)] pb-20">
      <section>
        <UserInfo />
      </section>
      <section>
        {reviewForm ? (
          <ReviewForm setReviewForm={setReviewForm} />
        ) : (
          <Hero setReviewForm={setReviewForm} />
        )}
      </section>
      <section className="flex flex-col gap-4 my-8">
        <h2 className="text-base font-medium text-white px-2">
          Users wrote 231 reviews
        </h2>
        <div className="flex flex-col gap-3.5">
          {Array.from({ length: 10 }).map((_, index) => (
            <Card key={index}>
              <div
                className="flex flex-col gap-2 rounded-xl p-1 text-[#353535]"
                style={{
                  background:
                    "radial-gradient(75.6% 174.31% at 52.86% 26.04%, #FFFFFF 0%, #F9FBFD 100%)",
                }}
              >
                <div className="flex items-center justify-between">
                  <h2 className="text-base font-bold">A Masterpiece</h2>
                  <div className="flex items-center gap-1">
                    <Star className={`w-4 fill-[#7FC9A0] text-[#7FC9A0]`} />
                    <p className=" font-medium text-xs">5 stars</p>
                  </div>
                </div>
                <p className="text-sm font-normal text-[#353535]/70 leading-[100%] tracking-[0%]">
                  An incredible movie with stunning visuals and a gripping
                  story.
                </p>
                <div className="flex items-center gap-1 mt-2">
                  <div className="flex items-center gap-1.5">
                    <div className="relative flex justify-center items-center rounded-full bg-[#B6A990] w-6 h-6">
                      <Image
                        src={images.reviewUser}
                        alt="avatar"
                        fill
                        sizes="10px 10px"
                        style={{
                          objectFit: "cover",
                        }}
                        className="w-fit h-fit rounded-full"
                      />
                    </div>
                    <p className="text-xs font-medium">Elowen Thorne</p>
                  </div>
                  <p className="text-xs font-medium">·</p>
                  <p className="text-xs font-medium">2 days ago</p>
                </div>{" "}
              </div>
            </Card>
          ))}
        </div>
      </section>
    </div>
  );
};

export default Reviews;
