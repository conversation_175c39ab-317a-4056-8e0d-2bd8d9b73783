"use client";
import React from "react";
import { MessageCircle, Star } from "lucide-react";
import Image from "next/image";
import images from "@/public/images";
import Button from "@/components/ui/button";

interface StarRatingProps {
  rating: number;
  size?: number;
}

const StarRating: React.FC<StarRatingProps> = ({ rating, size = 5 }) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;

  return (
    <div className="flex items-center gap-0.5">
      {[...Array(5)].map((_, index) => (
        <div key={index} className="relative">
          {index < fullStars ? (
            <Star
              className={`w-${size} h-${size} fill-[#7FC9A0] text-[#7FC9A0]`}
            />
          ) : index === fullStars && hasHalfStar ? (
            <div className="relative">
              <Star className={`w-${size} h-${size} text-[#7FC9A0]`} />
              <div
                className="absolute inset-0 overflow-hidden"
                style={{ width: "50%" }}
              >
                <Star
                  className={`w-${size} h-${size} fill-[#7FC9A0] text-[#7FC9A0]`}
                />
              </div>
            </div>
          ) : (
            <Star className={`w-${size} h-${size} text-[#7FC9A0]`} />
          )}
        </div>
      ))}
    </div>
  );
};

const hero = ({
  setReviewForm,
}: {
  setReviewForm: (value: boolean) => void;
}) => {
  return (
    <div className="flex flex-col gap-4 text-white">
      <div className="flex flex-col gap-2 rounded-xl relative px-4 pb-4 pt-2">
        <div
          style={{
            boxShadow: "0px 1px 10px 0px #00000040",
            background:
              "linear-gradient(183.04deg, rgba(217, 217, 217, 0) 33.2%, rgba(255, 255, 255, 0.4) 97.48%)",
            mixBlendMode: "soft-light",
          }}
          className="absolute inset-0 rounded-xl"
        />
        <div className="w-full flex items-center justify-between">
          <div className="flex items-center gap-2">
            <p className="font-bold text-xl ">4.5</p>
            <StarRating rating={4.5} />
          </div>

          <div
            className="flex items-center gap-3 px-2 py-1 rounded-xl"
            style={{
              background:
                "radial-gradient(82.43% 811.25% at 12.97% -295%, #ACEDC9 0%, #2E7D51 100%)",
            }}
          >
            <div className="flex flex-col  ">
              <p className="text-[10px] font-bold">Overall Score</p>
              <p className="text-[10px] font-light -mt-1.5">231 reviews</p>
            </div>

            <div className="flex items-center gap-1  ">
              <h2 className="text-xl font-bold">4.8</h2>
              <Star className={`w-5 h-5 fill-white`} />
            </div>
          </div>
        </div>
        <div className="flex flex-col gap-4 relative">
          <h1 className="text-4xl font-bold ">Lorem Ipsum Dolor Sit Amet</h1>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <div className="relative flex justify-center items-center rounded-full bg-[#B6A990] w-6 h-6">
                <Image
                  src={images.reviewUser}
                  alt="avatar"
                  fill
                  sizes="10px 10px"
                  style={{
                    objectFit: "cover",
                  }}
                  className="w-fit h-fit rounded-full"
                />
              </div>
              <p className="text-xs font-medium">Elowen Thorne</p>
            </div>
            <p className="text-xs font-medium">·</p>
            <p className="text-xs font-medium">2 days ago</p>
          </div>
          <p className="text-sm font-normal tracking-[0.01rem] leading-[1.25rem]">
            This app is fantastic! It offers a seamless experience, making it
            easy to navigate through its features. The design is sleek and
            user-friendly, ensuring that you can find what you need without any
            hassle.
          </p>

          <div className="flex items-center gap-2">
            <div className="flex items-end gap-1">
              <MessageCircle className="w-4 h-4" />
              <p className="text-xs font-bold">23 Coments</p>
            </div>
            <p className="text-xs font-medium">
              <span className="pr-1">·</span> Last comment 2 days ago
            </p>
          </div>
        </div>
      </div>
      <Button
        type="button"
        style={{
          background: `radial-gradient(82.43% 811.25% at 12.97% -295%, #ACEDC9 0%, #2E7D51 100%),
          radial-gradient(75.6% 174.31% at 52.86% 26.04%, #FFFFFF 0%, #F9FBFD 100%)`,
        }}
        className=""
        onClick={() => setReviewForm(true)}
      >
        <p className="text-2xl text-white font-bold">Post Review</p>
      </Button>
    </div>
  );
};

export default hero;
