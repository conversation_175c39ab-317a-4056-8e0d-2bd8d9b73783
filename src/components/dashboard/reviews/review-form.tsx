"use client";

import * as yup from "yup";
import { yupResolver } from "@hookform/resolvers/yup";
import { useForm } from "react-hook-form";
import { Ellip<PERSON>, Star } from "lucide-react";
import React, { useState } from "react";
import Button from "@/components/ui/button";

const schema = yup.object().shape({
  title: yup.string().required("Title is required"),
  description: yup
    .string()
    .required("Description is required")
    .max(200, "Description cannot exceed 200 characters"),
  rating: yup
    .number()
    .typeError("Rating must be a number")
    .required("Rating is required")
    .min(1, "Rating must be at least 1")
    .max(5, "Rating cannot be more than 5"),
});

type ReviewFormData = yup.InferType<typeof schema>;

const StarRating = ({
  rating,
  onChange,
}: {
  rating: number;
  onChange: (value: number) => void;
}) => {
  const fullStars = Math.floor(rating);
  const hasHalfStar = rating % 1 !== 0;

  return (
    <div className="w-full flex items-center gap-0.5 cursor-pointer justify-between px-4">
      {[...Array(5)].map((_, index) => {
        if (index < fullStars) {
          // Full star
          return (
            <span key={index} onClick={() => onChange(index + 1)}>
              <Star className="w-9 h-9 fill-[#7FC9A0] text-[#7FC9A0]" />
            </span>
          );
        } else if (index === fullStars && hasHalfStar) {
          // Half star using a gradient mask
          return (
            <span key={index} onClick={() => onChange(index + 1)}>
              <span className="relative w-9 h-9 block">
                <Star className="w-9 h-9 text-[#7FC9A0]" />
                <Star
                  className="w-9 h-9 fill-[#7FC9A0] text-[#7FC9A0] absolute top-0 left-0"
                  style={{ clipPath: "inset(0 50% 0 0)" }}
                />
              </span>
            </span>
          );
        } else {
          // Empty star
          return (
            <span key={index} onClick={() => onChange(index + 1)}>
              <Star className="w-9 h-9 text-[#7FC9A0]" />
            </span>
          );
        }
      })}
    </div>
  );
};

const ReviewForm = ({
  setReviewForm,
}: {
  setReviewForm: (value: boolean) => void;
}) => {
  const form = useForm<ReviewFormData>({
    defaultValues: {
      title: "",
      description: "",
      rating: 5,
    },
    resolver: yupResolver(schema),
    mode: "onBlur",
  });

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = form;

  const rating = watch("rating");
  const [localRating, setLocalRating] = useState<number>(rating);

  // Keep localRating in sync with form rating
  React.useEffect(() => {
    setLocalRating(rating);
  }, [rating]);

  const handleStarClick = (value: number) => {
    setValue("rating", value, { shouldValidate: true });
    setLocalRating(value);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    // Only allow numbers and one decimal
    if (!/^(\d*\.?\d*)$/.test(value)) return;

    // Allow empty input for typing
    if (value === "") {
      setValue("rating", value as unknown as number, { shouldValidate: true });
      setLocalRating(0);
      return;
    }

    let num = parseFloat(value);

    // Clamp between 1 and 5
    if (!isNaN(num)) {
      if (num < 1) num = 1;
      if (num > 5) num = 5;
      setValue("rating", num, { shouldValidate: true });
      setLocalRating(num);
    }
  };

  const description = watch("description") || "";
  const charCount = description.length;

  const onSubmit = (data: ReviewFormData) => {
    console.log(data);
  };

  return (
    <div className="flex flex-col text-[#353535] bg-white rounded-xl">
      <div className="pb-2 flex items-center justify-between border-b border-[#35393314] p-3">
        <h2 className="text-base font-bold">Leave a review</h2>
        <Ellipsis className="w-4 h-4 text-[#5F6D7E]" />
      </div>
      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-col gap-4 py-3.5 px-3"
      >
        <div className="flex flex-col gap-1">
          <label className="text-base font-bold" htmlFor="title">
            Title
          </label>
          <div className="flex flex-col gap-0.5">
            <input
              type="text"
              {...register("title")}
              className="py-3 px-2.5 bg-[#D6DDE5] border border-[#35393314] rounded-[0.375rem]"
            />
            {errors.title && (
              <p className="text-sm text-red-500">{errors.title.message}</p>
            )}
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <label className="text-base font-bold" htmlFor="rating">
            Rating{" "}
            <span className="text-sm font-normal">
              (Type the number or select the star)
            </span>
          </label>
          <div className="flex flex-col gap-0.5">
            <div className="flex items-center bg-[#D6DDE5] border border-[#35393314] rounded-[0.375rem]">
              <input
                type="number"
                step="0.1"
                min={1}
                max={5}
                value={localRating}
                onChange={handleInputChange}
                pattern="^\\d*\\.?\\d*$"
                className="py-2 px-3 bg-[#7FC9A0] rounded-[0.375rem] w-24 text-4xl font-bold text-white text-center outline-none"
              />
              <StarRating rating={localRating} onChange={handleStarClick} />
            </div>
            {errors.rating && (
              <p className="text-sm text-red-500">{errors.rating.message}</p>
            )}
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <label className="text-base font-bold" htmlFor="description">
            Description
          </label>
          <div className="flex flex-col gap-0.5 relative">
            <textarea
              {...register("description")}
              rows={6}
              maxLength={200}
              className="py-3 px-2.5 bg-[#D6DDE5] border border-[#35393314] rounded-[0.375rem] resize-vertical min-h-[120px]"
              style={{ minHeight: "120px" }}
            />
            <span className="absolute bottom-2 right-3 text-xs text-gray-500 select-none">
              {charCount}/200
            </span>
            {errors.description && (
              <p className="text-sm text-red-500">
                {errors.description.message}
              </p>
            )}
          </div>
        </div>
        <div className="flex flex-col gap-3">
          <Button
            type="submit"
            style={{
              background: `radial-gradient(82.43% 811.25% at 12.97% -295%, #ACEDC9 0%, #2E7D51 100%),
          radial-gradient(75.6% 174.31% at 52.86% 26.04%, #FFFFFF 0%, #F9FBFD 100%)`,
            }}
            className=""
          >
            <p className="text-2xl text-white font-bold">Post Review</p>
          </Button>
          <Button
            type="button"
            className="bg-[#A5A5A5] "
            onClick={() => setReviewForm(false)}
          >
            <p className="text-2xl text-white font-bold">Cancel</p>
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ReviewForm;
