import Dock from "./dock";
import Header from "./header";

type DashboardLayoutProps = {
  children: React.ReactNode;
};

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  return (
    <main className="flex flex-col min-h-svh pb-[env(safe-area-inset-bottom)] ">
      <Header />
      <div className="grow wrapper">{children}</div>
      <Dock />
    </main>
  );
};

export default DashboardLayout;
