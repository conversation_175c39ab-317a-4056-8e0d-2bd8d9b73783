import Image from "next/image";
import Link from "next/link";

const Header = () => {
  return (
    <div className="wrapper">
      <div className="flex items-center justify-between py-5"> 
        <div className="">
          <Image
            src="/images/logo-full.png"
            alt="Logo"
            width={500}
            height={500}
            quality={100}
            className="w-[6.3125rem] h-[1.66725rem]"
          />
        </div>

        <div className="flex items-center justify-end gap-3">
        <Link
            href="/"
            className=""
          >
            <Image
              src="/icons/user.svg"
              alt="User"
              width={40}
              height={40}
              className="size-[1.5625rem]"
            />
          </Link>
          <Link
            href="/"
            className=""
          >
            <Image
              src="/icons/menu.svg"
              alt="Menu"
              width={40}
              height={40}
              className="size-[2rem]"
            />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Header;
